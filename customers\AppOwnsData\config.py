# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.


class BaseConfig(object):

    # Can be set to 'MasterUser' or 'ServicePrincipal'
    AUTHENTICATION_MODE = "ServicePrincipal"

    # Workspace Id in which the report is present
    WORKSPACE_ID = "e2e05478-cb34-4909-b67e-dca0cc9c4cd4"

    # Report Id for which Embed token needs to be generated
    REPORT_ID = "81364259-cdcd-4939-bf2a-50d0a56c9db9"

    # Id of the Azure tenant in which AAD app and Power BI report is hosted. Required only for ServicePrincipal authentication mode.
    TENANT_ID = "e8679cd0-0462-4591-bfc5-a55bb33d5560"

    # Client Id (Application Id) of the AAD app
    CLIENT_ID = "76ff43b5-235b-4347-829e-d12886e99f83"

    # Client Secret (App Secret) of the AAD app. Required only for ServicePrincipal authentication mode.
    CLIENT_SECRET = "****************************************"

    # Scope Base of AAD app. Use the below configuration to use all the permissions provided in the AAD app through Azure portal.
    SCOPE_BASE = ["https://analysis.windows.net/powerbi/api/.default"]

    # URL used for initiating authorization request
    AUTHORITY_URL = "https://login.microsoftonline.com/organizations"

    # Master user email address. Required only for MasterUser authentication mode.
    POWER_BI_USER = "<EMAIL>"

    # Master user email password. Required only for MasterUser authentication mode.
    POWER_BI_PASS = "Shovil#98057"

    # Power Automate Flow Configuration
    # Power Automate Flow URL for data export
    # POWER_AUTOMATE_FLOW_URL = "https://prod-57.southeastasia.logic.azure.com:443/workflows/dfec013091bb4fb9ad130db058ec816a/triggers/manual/paths/invoke?api-version=2016-06-01"

    POWER_AUTOMATE_FLOW_URL = "https://prod-57.southeastasia.logic.azure.com:443/workflows/dfec013091bb4fb9ad130db058ec816a/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=B5yTyu57FGUMhWsU0iSnH-AmhKKFb-7FK5iYj_-AI38"

    # Power Automate HTTP method: 'POST' or 'GET'
    POWER_AUTOMATE_HTTP_METHOD = "POST"

    # Power Automate authentication method: 'none', 'bearer_token', 'api_key'
    POWER_AUTOMATE_AUTH_METHOD = "none"

    # Power Automate API Key (if using api_key authentication)
    POWER_AUTOMATE_API_KEY = ""

    # Power Automate Bearer Token (if using bearer_token authentication)
    POWER_AUTOMATE_BEARER_TOKEN = ""
