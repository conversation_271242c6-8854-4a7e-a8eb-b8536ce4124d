// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.

$(function () {
    var reportContainer = $("#report-container").get(0);
    powerbi.bootstrap(reportContainer, { type: "report" });

    var models = window["powerbi-client"].models;
    var reportLoadConfig = {
        type: "report",
        tokenType: models.TokenType.Embed
    };

    let report;

    $.ajax({
        type: "GET",
        url: "/getembedinfo",
        dataType: "json",
        success: function (data) {
            embedData = $.parseJSON(JSON.stringify(data));
            reportLoadConfig.accessToken = embedData.accessToken;
            reportLoadConfig.embedUrl = embedData.reportConfig[0].embedUrl;
            tokenExpiry = embedData.tokenExpiry;

            report = powerbi.embed(reportContainer, reportLoadConfig);

            report.on("loaded", function () {
                console.log("Report load successful");
            });

            report.on("rendered", function () {
                console.log("Report render successful");
            });

            report.off("error");
            report.on("error", function (event) {
                var errorMsg = event.detail;
                console.error(errorMsg);
                return;
            });

            report.on("dataSelected", function (event) {
                var selectionData = event.detail;
                console.log("Raw selection data:", selectionData);

                if (selectionData && selectionData.dataPoints) {
                    const selected = selectionData.dataPoints.map(dp => {
                        return {
                            visualTitle: selectionData.visualTitle,
                            category: dp.identityFields?.[0]?.displayName || 'Unknown',
                            value: dp.values?.[0] || 'N/A'
                        };
                    });

                    console.log("Parsed selection:", selected);
                    // Optional: sendToFlow(selected);
                }
            });
        },
        error: function (err) {
            var errorContainer = $(".error-container");
            $(".embed-container").hide();
            errorContainer.show();

            var errMessageHtml = "<strong> Error Details: </strong> <br/>" + $.parseJSON(err.responseText)["errorMsg"];
            errMessageHtml = errMessageHtml.split("\n").join("<br/>");
            errorContainer.html(errMessageHtml);
        }
    });

    // ✅ Print as PDF
    $("#print-report").on("click", function () {
        if (report) {
            report.print()
                .then(() => console.log("Print dialog opened"))
                .catch(err => console.error("Print failed:", err));
        } else {
            console.error("Report is not loaded yet.");
        }
    });

    // ✅ Export data from Pie or Stacked Chart and send to Power Automate
    $("#export-data").on("click", async function () {
        if (!report) return console.error("Report not available yet.");

        try {
            const pages = await report.getPages();
            const activePage = pages.find(p => p.isActive);
            const visuals = await activePage.getVisuals();

            // 🔍 Look for Pie Chart or Stacked Column Chart
            const chartVisual = visuals.find(v =>
                v.type.toLowerCase().includes("donut") ||
                v.type.toLowerCase().includes("stackedcolumn") ||
                v.type.toLowerCase().includes("column") // fallback
            );

            if (!chartVisual) return alert("No Pie or Stacked Chart visual found.");

            const exportResult = await chartVisual.exportData(models.ExportDataType.Summarized, 1000);

            console.log("Exported summarized chart data:", exportResult.data);

            // Send data to backend endpoint which handles Power Automate authentication
            const response = await fetch("/sendtoflow", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ data: exportResult.data })
            });

            const result = await response.json();

            if (response.ok) {
                alert(result.message || "Data sent to Power Automate successfully!");
            } else {
                console.error("Power Automate error:", result.errorMsg);
                alert("Failed to send data to Power Automate: " + (result.errorMsg || "Unknown error"));
            }
        } catch (err) {
            console.error("Error exporting chart data:", err);
            alert("Failed to export chart data: " + err.message);
        }
    });
});
