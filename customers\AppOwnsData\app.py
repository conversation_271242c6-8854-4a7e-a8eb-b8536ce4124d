# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import json
import os

import requests
from flask import Flask, jsonify, render_template, request, send_from_directory
from services.aadservice import AadService
from services.pbiembedservice import PbiEmbedService
from utils import Utils

# Initialize the Flask app
app = Flask(__name__)

# Load configuration
app.config.from_object("config.BaseConfig")


@app.route("/")
def index():
    """Returns a static HTML page"""

    return render_template("index.html")


@app.route("/getembedinfo", methods=["GET"])
def get_embed_info():
    """Returns report embed configuration"""

    config_result = Utils.check_config(app)
    if config_result is not None:
        return json.dumps({"errorMsg": config_result}), 500
    try:
        embed_info = PbiEmbedService().get_embed_params_for_single_report(
            app.config["WORKSPACE_ID"], app.config["REPORT_ID"]
        )
        return embed_info
    except Exception as ex:
        return json.dumps({"errorMsg": str(ex)}), 500


@app.route("/sendtoflow", methods=["POST"])
def send_to_flow():
    """Sends data to Power Automate flow with proper authentication"""

    try:
        # Get the data from the request
        data = request.get_json()
        if not data:
            return jsonify({"errorMsg": "No data provided"}), 400

        # Prepare headers for Power Automate request
        headers = {"Content-Type": "application/json"}

        # Add authentication based on configuration
        auth_method = app.config.get("POWER_AUTOMATE_AUTH_METHOD", "none")

        if auth_method == "bearer_token":
            bearer_token = app.config.get("POWER_AUTOMATE_BEARER_TOKEN")
            if bearer_token:
                headers["Authorization"] = f"Bearer {bearer_token}"
            else:
                # Try to use the existing Azure AD token
                try:
                    access_token = AadService.get_access_token()
                    headers["Authorization"] = f"Bearer {access_token}"
                except Exception:
                    # If Azure AD token fails, continue without authentication
                    pass
        elif auth_method == "api_key":
            api_key = app.config.get("POWER_AUTOMATE_API_KEY")
            if api_key:
                headers["X-API-Key"] = api_key

        # Get the Power Automate flow URL
        flow_url = app.config.get("POWER_AUTOMATE_FLOW_URL")
        if not flow_url:
            return jsonify({"errorMsg": "Power Automate flow URL not configured"}), 500

        # Make the request to Power Automate
        response = requests.post(flow_url, json=data, headers=headers, timeout=30)

        # Check if the request was successful
        if response.status_code == 200 or response.status_code == 202:
            return jsonify({"message": "Data sent to Power Automate successfully!"})
        else:
            return (
                jsonify(
                    {
                        "errorMsg": f"Power Automate request failed with status {response.status_code}: {response.text}"
                    }
                ),
                response.status_code,
            )

    except requests.exceptions.RequestException as ex:
        return jsonify({"errorMsg": f"Network error: {str(ex)}"}), 500
    except Exception as ex:
        return (
            jsonify({"errorMsg": f"Error sending data to Power Automate: {str(ex)}"}),
            500,
        )


@app.route("/favicon.ico", methods=["GET"])
def getfavicon():
    """Returns path of the favicon to be rendered"""

    return send_from_directory(
        os.path.join(app.root_path, "static"),
        "img/favicon.ico",
        mimetype="image/vnd.microsoft.icon",
    )


if __name__ == "__main__":
    app.run()
